<?php $__env->startSection('css'); ?>
    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')); ?>">
    <style>
        /* IMSAA Color Scheme */
        :root {
            --primary-blue: #3498db;
            --darker-blue: #2980b9;
            --red-accent: #e74c3c;
            --professional-gray: #2c3e50;
            --light-gray: #34495e;
            --success-green: #27ae60;
            --warning-orange: #f39c12;
        }

        /* Modern Card Styles */
        .student-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .student-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced Filter Bar */
        .filter-bar {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Statistics Cards */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--darker-blue));
        }

        /* Enhanced Table Styles */
        .table-modern {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .table-modern thead th {
            background: linear-gradient(135deg, var(--professional-gray), var(--light-gray));
            color: white;
            font-weight: 600;
            border: none;
            padding: 16px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .table-modern thead th:hover {
            background: var(--darker-blue);
        }
        .table-modern tbody tr {
            transition: all 0.2s ease;
        }
        .table-modern tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
            transform: scale(1.01);
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        /* Action Buttons */
        .btn-action {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
        }
        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .status-rempli {
            background: linear-gradient(135deg, var(--success-green), #2ecc71);
            color: white;
        }
        .status-non-rempli {
            background: linear-gradient(135deg, var(--warning-orange), #e67e22);
            color: white;
        }

        /* Modal Enhancements */
        .modal-xl {
            max-width: 95%;
        }
        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-blue), var(--darker-blue));
            color: white;
            border-radius: 16px 16px 0 0;
            padding: 20px 24px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .modal-xl {
                max-width: 98%;
                margin: 1rem auto;
            }
        }

        @media (max-width: 992px) {
            .stats-card .card-body {
                padding: 1rem;
            }
            .table-modern thead th {
                padding: 12px 8px;
                font-size: 0.9rem;
            }
            .btn-action {
                padding: 6px 12px;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 768px) {
            .filter-bar {
                padding: 16px;
            }
            .stats-card {
                margin-bottom: 16px;
            }
            .table-responsive {
                border-radius: 8px;
            }

            /* Mobile-first table design */
            .table-modern {
                font-size: 0.85rem;
            }
            .table-modern thead th {
                padding: 8px 6px;
            }
            .table-modern tbody td {
                padding: 8px 6px;
            }

            /* Stack action buttons vertically on mobile */
            .btn-group {
                flex-direction: column;
                gap: 4px;
            }
            .btn-group .btn {
                width: 100%;
                margin: 0;
            }

            /* Improve modal on mobile */
            .modal-dialog {
                margin: 0.5rem;
            }
            .modal-content {
                border-radius: 12px;
            }

            /* Hide less important columns on mobile */
            .d-none-mobile {
                display: none !important;
            }
        }

        @media (max-width: 576px) {
            .hero-section h1 {
                font-size: 1.5rem;
            }
            .breadcrumb {
                font-size: 0.85rem;
            }

            /* Compact stats cards on small screens */
            .stats-card .fs-3 {
                font-size: 1.5rem !important;
            }
            .stats-card .card-body {
                padding: 0.75rem;
            }

            /* Single column layout for filters */
            .filter-bar .row > div {
                margin-bottom: 1rem;
            }

            /* Simplified table for very small screens */
            .table-mobile-simple {
                display: block;
            }
            .table-mobile-simple thead {
                display: none;
            }
            .table-mobile-simple tbody,
            .table-mobile-simple tr,
            .table-mobile-simple td {
                display: block;
                width: 100%;
            }
            .table-mobile-simple tr {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 1rem;
                padding: 1rem;
                background: white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .table-mobile-simple td {
                border: none;
                padding: 0.25rem 0;
                position: relative;
                padding-left: 50%;
            }
            .table-mobile-simple td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 0;
                width: 45%;
                font-weight: bold;
                color: var(--professional-gray);
            }
        }

        /* Accessibility Improvements */
        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            border-color: var(--primary-blue);
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .stats-card {
                border: 2px solid #000;
            }
            .table-modern thead th {
                border: 1px solid #000;
            }
            .btn-action {
                border: 2px solid currentColor;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .student-card,
            .stats-card,
            .btn-action,
            .table-modern tbody tr {
                transition: none;
            }
            .fade-in,
            .slide-in {
                animation: none;
            }
        }

        /* Dark mode support (if needed in future) */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1a1a1a;
                --text-color: #ffffff;
                --card-bg: #2d2d2d;
            }
        }

        /* Print styles */
        @media print {
            .btn,
            .modal,
            .filter-bar,
            .pagination {
                display: none !important;
            }
            .table-modern {
                border-collapse: collapse;
            }
            .table-modern th,
            .table-modern td {
                border: 1px solid #000 !important;
                padding: 8px !important;
            }
            .stats-card {
                break-inside: avoid;
                margin-bottom: 1rem;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <!-- jQuery (required for DataTables plugin) -->
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<div>
    <!-- Enhanced Hero Section -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center py-3">
                <div class="fade-in">
                    <h1 class="h2 fw-bold mb-2 text-primary">
                        <i class="fa fa-users me-2"></i>
                        Gestion des Informations Étudiants <?php dump($isLoading); ?>
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-alt">
                            <li class="breadcrumb-item">
                                <a href="#" class="text-decoration-none">
                                    <i class="fa fa-home me-1"></i>Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fa fa-user-graduate me-1"></i>Informations Étudiants
                            </li>
                        </ol>
                    </nav>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2 mt-3 mt-md-0 slide-in">
                    <button type="button" class="btn btn-outline-primary btn-action"
                            wire:click="toggleFilters"
                            title="Basculer les filtres">
                        <i class="fa fa-filter me-1"></i>
                        <span class="d-none d-sm-inline">Filtres</span>
                    </button>

                    <button type="button" class="btn btn-outline-secondary btn-action"
                            wire:click="toggleCompactView"
                            title="Vue compacte">
                        <i class="fa fa-<?php echo e($compactView ? 'expand' : 'compress'); ?> me-1"></i>
                        <span class="d-none d-sm-inline"><?php echo e($compactView ? 'Étendue' : 'Compacte'); ?></span>
                    </button>

                    <div class="dropdown">
                        <button class="btn btn-primary btn-action dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-cog me-1"></i>
                            <span class="d-none d-sm-inline">Actions</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="#" wire:click="clearFilters">
                                    <i class="fa fa-eraser me-2"></i>Effacer les filtres
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="window.print()">
                                    <i class="fa fa-print me-2"></i>Imprimer
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Enhanced Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Enhanced Statistics Dashboard -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-primary text-white fade-in">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-users fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold"><?php echo e($totalEtudiants); ?></div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Total Étudiants</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-success text-white fade-in" style="animation-delay: 0.1s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-check-circle fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold"><?php echo e($totalRemplis); ?></div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Dossiers Complets</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-warning text-white fade-in" style="animation-delay: 0.2s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold"><?php echo e($totalNonRemplis); ?></div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">À Compléter</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="block block-rounded stats-card bg-info text-white fade-in" style="animation-delay: 0.3s">
                    <div class="block-content p-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fa fa-percentage fa-2x opacity-75"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fs-3 fw-bold">
                                    <?php echo e($totalEtudiants > 0 ? round(($totalRemplis / $totalEtudiants) * 100, 2) : 0); ?>%
                                </div>
                                <div class="fs-sm fw-semibold text-uppercase opacity-75">Taux Complétion</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters Section -->
        <?php if($showFilters): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="block block-rounded filter-bar slide-in">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fa fa-filter me-2 text-primary"></i>
                            Filtres de Recherche
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                wire:click="clearFilters"
                                title="Effacer tous les filtres">
                            <i class="fa fa-eraser me-1"></i>Effacer
                        </button>
                    </div>

                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-search me-1 text-muted"></i>Recherche
                            </label>
                            <div class="input-group">
                                <input type="search"
                                       wire:model.debounce.300ms="query"
                                       class="form-control"
                                       placeholder="Nom, prénom, matricule...">
                                <span class="input-group-text">
                                    <i class="fa fa-search"></i>
                                </span>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-layer-group me-1 text-muted"></i>Niveau
                            </label>
                            <select wire:model="filtreNiveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-calendar me-1 text-muted"></i>Année Universitaire
                            </label>
                            <select wire:model="filtreAnnee" class="form-select">
                                <option value="">Toutes les années</option>
                                <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-clipboard-check me-1 text-muted"></i>État du Dossier
                            </label>
                            <select wire:model="filtreRempli" class="form-select">
                                <option value="">Tous les étudiants</option>
                                <option value="non_rempli">Dossiers incomplets</option>
                                <option value="rempli">Dossiers complets</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Enhanced Students List -->
            <div class="col-lg-8">
                <div class="block block-rounded student-card">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-list me-2 text-primary"></i>
                            Liste des Étudiants
                            <span class="badge bg-primary ms-2"><?php echo e($etus->total()); ?></span>
                        </h3>
                        <div class="block-options">
                            <div class="dropdown me-2">
                                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-sort me-1"></i>Trier
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('created_at')">
                                            <i class="fa fa-calendar me-2"></i>Date d'inscription
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('nom')">
                                            <i class="fa fa-sort-alpha-down me-2"></i>Nom (A-Z)
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" wire:click="sortBy('niveau_id')">
                                            <i class="fa fa-layer-group me-2"></i>Niveau
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    data-toggle="block-option" data-action="fullscreen_toggle"
                                    title="Plein écran">
                                <i class="fa fa-expand-alt"></i>
                            </button>
                        </div>
                    </div>

                    <div class="block-content block-content-full position-relative">
                        <!-- Loading Overlay -->
                        <?php if($isLoading): ?>
                        <div class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="table-responsive">
                            <table class="table table-modern table-hover <?php echo e($compactView ? 'table-sm' : ''); ?>"
                                   role="table"
                                   aria-label="Liste des étudiants"
                                   aria-describedby="students-table-description">
                                <caption id="students-table-description" class="visually-hidden">
                                    Tableau listant <?php echo e($etus->total()); ?> étudiants avec leurs informations personnelles et académiques
                                </caption>
                                <thead>
                                    <tr role="row">
                                        <th wire:click="sortBy('nom')"
                                            style="cursor: pointer;"
                                            title="Trier par nom"
                                            role="columnheader"
                                            aria-sort="<?php echo e($sortField === 'nom' ? ($sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'); ?>"
                                            tabindex="0"
                                            onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
                                            <i class="fa fa-user me-1" aria-hidden="true"></i>Nom et Prénom
                                            <?php if($sortField === 'nom'): ?>
                                                <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1"
                                                   aria-label="Trié par ordre <?php echo e($sortDirection === 'asc' ? 'croissant' : 'décroissant'); ?>"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th role="columnheader">
                                            <i class="fa fa-layer-group me-1" aria-hidden="true"></i>Niveau
                                        </th>
                                        <th role="columnheader" class="d-none d-md-table-cell">
                                            <i class="fa fa-calendar me-1" aria-hidden="true"></i>Année
                                        </th>
                                        <th role="columnheader">
                                            <i class="fa fa-clipboard-check me-1" aria-hidden="true"></i>État
                                        </th>
                                        <th class="text-center" style="width: 140px;" role="columnheader">
                                            <i class="fa fa-cogs me-1" aria-hidden="true"></i>Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $etus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $etu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr class="fade-in" role="row">
                                            <td role="gridcell"
                                                class="fw-semibold"
                                                data-label="Nom et Prénom">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                             style="width: 32px; height: 32px; font-size: 0.8rem;"
                                                             aria-label="Avatar de <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>">
                                                            <?php echo e(strtoupper(substr($etu->user->nom, 0, 1))); ?><?php echo e(strtoupper(substr($etu->user->prenom ?? '', 0, 1))); ?>

                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?></div>
                                                        <?php if($etu->user->matricule): ?>
                                                            <small class="text-muted"><?php echo e($etu->user->matricule); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td role="gridcell" data-label="Niveau">
                                                <?php if($etu->niveau == null): ?>
                                                    <span class="badge bg-warning"
                                                          aria-label="Niveau non défini">
                                                        <i class="fa fa-exclamation-triangle me-1" aria-hidden="true"></i>Non défini
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-info"
                                                          aria-label="Niveau <?php echo e($etu->niveau->nom); ?>"><?php echo e($etu->niveau->nom); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td role="gridcell"
                                                class="d-none d-md-table-cell"
                                                data-label="Année">
                                                <span class="badge bg-secondary"
                                                      aria-label="Année universitaire <?php echo e($etu->annee->nom); ?>"><?php echo e($etu->annee->nom); ?></span>
                                            </td>
                                            <td role="gridcell" data-label="État">
                                                <?php if($etu->parcours == null): ?>
                                                    <span class="status-badge status-non-rempli"
                                                          aria-label="Dossier à compléter">
                                                        <i class="fa fa-clock me-1" aria-hidden="true"></i>À compléter
                                                    </span>
                                                <?php else: ?>
                                                    <span class="status-badge status-rempli"
                                                          aria-label="Dossier complet">
                                                        <i class="fa fa-check me-1" aria-hidden="true"></i>Complet
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center" role="gridcell" data-label="Actions">
                                                <div class="btn-group" role="group" aria-label="Actions pour <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>">
                                                    <?php if($etu->parcours == null): ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-warning btn-action"
                                                                wire:click="openEditModal(<?php echo e($etu->user->id); ?>)"
                                                                aria-label="Compléter le dossier de <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>">
                                                            <i class="fa fa-edit" aria-hidden="true"></i>
                                                            <span class="d-none d-lg-inline ms-1">Remplir</span>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-success btn-action"
                                                                wire:click="openEditModal(<?php echo e($etu->user->id); ?>)"
                                                                aria-label="Modifier les informations de <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>">
                                                            <i class="fa fa-edit" aria-hidden="true"></i>
                                                            <span class="d-none d-lg-inline ms-1">Modifier</span>
                                                        </button>
                                                    <?php endif; ?>

                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-info btn-action"
                                                            wire:click="openPaymentModal(<?php echo e($etu->user->id); ?>)"
                                                            aria-label="Voir les paiements de <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>">
                                                        <i class="fa fa-credit-card" aria-hidden="true"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4" role="gridcell">
                                                <div class="text-muted">
                                                    <i class="fa fa-search fa-2x mb-2" aria-hidden="true"></i>
                                                    <p class="mb-0">Aucun étudiant trouvé avec les critères actuels</p>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-primary mt-2"
                                                            wire:click="clearFilters"
                                                            aria-label="Effacer tous les filtres de recherche">
                                                        Effacer les filtres
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Enhanced Pagination -->
                        <?php if($etus->hasPages()): ?>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted">
                                Affichage de <?php echo e($etus->firstItem()); ?> à <?php echo e($etus->lastItem()); ?>

                                sur <?php echo e($etus->total()); ?> résultats
                            </div>
                            <nav aria-label="Navigation des étudiants">
                                <?php echo e($etus->links()); ?>

                            </nav>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Sidebar -->
            <div class="col-lg-4">
                <div class="block block-rounded stats-card">
                    <div class="block-header block-header-default">
                        <h3 class="block-title">
                            <i class="fa fa-chart-pie me-2 text-primary"></i>
                            Répartition par Parcours
                        </h3>
                        <div class="block-options">
                            <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="refreshStats()"
                                    title="Actualiser les statistiques">
                                <i class="fa fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="block-content">
                        <?php if($etuStatus->count() > 0): ?>
                            <!-- Progress Bars for Each Parcours -->
                            <?php $__currentLoopData = $etuStatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($parcour->etu_count > 0): ?>
                                <div class="mb-3 fade-in" style="animation-delay: <?php echo e($index * 0.1); ?>s">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-semibold text-truncate" style="max-width: 60%;">
                                            <?php echo e($parcour->nom); ?>

                                        </span>
                                        <div class="text-end">
                                            <span class="badge bg-primary"><?php echo e($parcour->etu_count); ?></span>
                                            <small class="text-muted ms-1">
                                                (<?php echo e($totalEtudiants > 0 ? round(($parcour->etu_count / $totalEtudiants) * 100) : 0); ?>%)
                                            </small>
                                        </div>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-gradient"
                                             role="progressbar"
                                             style="width: <?php echo e($totalEtudiants > 0 ? ($parcour->etu_count / $totalEtudiants) * 100 : 0); ?>%;
                                                    background: linear-gradient(90deg,
                                                        hsl(<?php echo e(($index * 60) % 360); ?>, 70%, 50%),
                                                        hsl(<?php echo e((($index * 60) + 30) % 360); ?>, 70%, 60%));"
                                             aria-valuenow="<?php echo e($parcour->etu_count); ?>"
                                             aria-valuemin="0"
                                             aria-valuemax="<?php echo e($totalEtudiants); ?>">
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <!-- Summary Card -->
                            <div class="mt-4 p-3 bg-light rounded">
                                <h6 class="mb-2">
                                    <i class="fa fa-info-circle me-1 text-info"></i>
                                    Résumé
                                </h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-primary"><?php echo e($etuStatus->where('etu_count', '>', 0)->count()); ?></div>
                                        <small class="text-muted">Parcours actifs</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-success"><?php echo e($totalRemplis); ?></div>
                                        <small class="text-muted">Complets</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fs-5 fw-bold text-warning"><?php echo e($totalNonRemplis); ?></div>
                                        <small class="text-muted">En attente</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="mt-3">
                                <h6 class="mb-2">
                                    <i class="fa fa-bolt me-1 text-warning"></i>
                                    Actions Rapides
                                </h6>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            wire:click="$set('filtreRempli', 'non_rempli')">
                                        <i class="fa fa-exclamation-triangle me-1"></i>
                                        Voir les dossiers incomplets
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success"
                                            wire:click="$set('filtreRempli', 'rempli')">
                                        <i class="fa fa-check-circle me-1"></i>
                                        Voir les dossiers complets
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fa fa-chart-pie fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Aucune donnée disponible</p>
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                        wire:click="loadStatistics">
                                    <i class="fa fa-refresh me-1"></i>Actualiser
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Modern Student Edit Modal -->
    <?php if($showEditModal): ?>
    <div class="modal fade show d-block"
         tabindex="-1"
         role="dialog"
         aria-labelledby="editModalTitle"
         aria-describedby="editModalDescription"
         aria-modal="true"
         style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 id="editModalTitle" class="modal-title d-flex align-items-center">
                        <i class="fa fa-user-edit me-2" aria-hidden="true"></i>
                        <?php if(isset($editUser['nom'])): ?>
                            Modifier étudiant: <strong class="ms-1"><?php echo e($editUser['nom'] ?? ''); ?> <?php echo e($editUser['prenom'] ?? ''); ?></strong>
                        <?php else: ?>
                            Informations de l'étudiant
                        <?php endif; ?>
                    </h5>
                    <button type="button"
                            class="btn-close"
                            wire:click="closeAllModals"
                            aria-label="Fermer la fenêtre de modification"></button>
                </div>
                <div class="modal-body">
                    <?php if(isset($editUser['id'])): ?>
                        <!-- Student Info Header -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 48px; height: 48px; font-size: 1.2rem;">
                                                <?php echo e(strtoupper(substr($editUser['nom'] ?? '', 0, 1))); ?><?php echo e(strtoupper(substr($editUser['prenom'] ?? '', 0, 1))); ?>

                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1"><?php echo e($editUser['nom'] ?? ''); ?> <?php echo e($editUser['prenom'] ?? ''); ?></h5>
                                            <div class="text-muted">
                                                <?php if($editUser['matricule'] ?? false): ?>
                                                    <i class="fa fa-id-card me-1"></i><?php echo e($editUser['matricule']); ?>

                                                <?php else: ?>
                                                    <i class="fa fa-exclamation-triangle me-1 text-warning"></i>Matricule à générer
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-info">ID: <?php echo e($editUser['id'] ?? ''); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form wire:submit.prevent="updateUser"
                              role="form"
                              aria-labelledby="editModalTitle"
                              novalidate>
                            <div id="editModalDescription" class="visually-hidden">
                                Formulaire de modification des informations personnelles et académiques de l'étudiant
                            </div>

                            <!-- Loading Overlay -->
                            <?php if($isLoading): ?>
                            <div class="loading-overlay"
                                 role="status"
                                 aria-live="polite"
                                 aria-label="Mise à jour en cours">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Mise à jour en cours...</span>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="row">
                                <!-- Student Personal Info -->
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm mb-4">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-user me-2"></i>Informations personnelles
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="editUser-nom" class="form-label fw-semibold">
                                                    <i class="fa fa-user me-1 text-muted" aria-hidden="true"></i>Nom <span class="text-danger" aria-label="obligatoire">*</span>
                                                </label>
                                                <input type="text"
                                                       id="editUser-nom"
                                                       class="form-control <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       wire:model.defer="editUser.nom"
                                                       placeholder="Entrez le nom de famille"
                                                       aria-required="true"
                                                       aria-describedby="<?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> editUser-nom-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> editUser-nom-help"
                                                       autocomplete="family-name">
                                                <div id="editUser-nom-help" class="form-text visually-hidden">
                                                    Saisissez le nom de famille de l'étudiant
                                                </div>
                                                <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div id="editUser-nom-error" class="invalid-feedback" role="alert"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="editUser-prenom" class="form-label fw-semibold">
                                                        <i class="fa fa-user me-1 text-muted" aria-hidden="true"></i>Prénom
                                                    </label>
                                                    <input type="text"
                                                           id="editUser-prenom"
                                                           class="form-control <?php $__errorArgs = ['editUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.prenom"
                                                           placeholder="Entrez le prénom"
                                                           aria-describedby="<?php $__errorArgs = ['editUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> editUser-prenom-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           autocomplete="given-name">
                                                    <?php $__errorArgs = ['editUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div id="editUser-prenom-error" class="invalid-feedback" role="alert"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="editUser-sexe" class="form-label fw-semibold">
                                                        <i class="fa fa-venus-mars me-1 text-muted" aria-hidden="true"></i>Sexe <span class="text-danger" aria-label="obligatoire">*</span>
                                                    </label>
                                                    <select id="editUser-sexe"
                                                            class="form-select <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            wire:model.defer="editUser.sexe"
                                                            aria-required="true"
                                                            aria-describedby="<?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> editUser-sexe-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                        <option value="">Sélectionner le sexe</option>
                                                        <option value="H">👨 Homme</option>
                                                        <option value="F">👩 Femme</option>
                                                    </select>
                                                    <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div id="editUser-sexe-error" class="invalid-feedback" role="alert"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-calendar me-1 text-muted"></i>Date de naissance <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="date"
                                                           class="form-control <?php $__errorArgs = ['editUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.date_naissance"
                                                           max="<?php echo e(date('Y-m-d', strtotime('-16 years'))); ?>">
                                                    <?php $__errorArgs = ['editUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-map-marker-alt me-1 text-muted"></i>Lieu de naissance <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.lieu_naissance"
                                                           placeholder="Ville de naissance">
                                                    <?php $__errorArgs = ['editUser.lieu_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-envelope me-1 text-muted"></i>Email
                                                    </label>
                                                    <input type="email" class="form-control <?php $__errorArgs = ['editUser.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.email"
                                                           placeholder="<EMAIL>">
                                                    <?php $__errorArgs = ['editUser.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-id-card me-1 text-muted"></i>Matricule
                                                    </label>
                                                    <input type="text" class="form-control bg-light"
                                                           wire:model.defer="editUser.matricule"
                                                           readonly>
                                                    <small class="text-muted">
                                                        <i class="fa fa-info-circle me-1"></i>Généré automatiquement lors de la sauvegarde
                                                    </small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone principal <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="tel" class="form-control <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.telephone1"
                                                           placeholder="+261 XX XX XXX XX">
                                                    <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone secondaire
                                                    </label>
                                                    <input type="tel" class="form-control <?php $__errorArgs = ['editUser.telephone2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.telephone2"
                                                           placeholder="+261 XX XX XXX XX">
                                                    <?php $__errorArgs = ['editUser.telephone2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">
                                                    <i class="fa fa-home me-1 text-muted"></i>Adresse
                                                </label>
                                                <textarea class="form-control <?php $__errorArgs = ['editUser.adresse'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                          rows="3"
                                                          wire:model.defer="editUser.adresse"
                                                          placeholder="Adresse complète de résidence"></textarea>
                                                <?php $__errorArgs = ['editUser.adresse'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Academic Info and Family Info -->
                                <div class="col-md-6">
                                    <!-- Academic Information Card -->
                                    <div class="card border-0 shadow-sm mb-4">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-graduation-cap me-2"></i>Informations académiques
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-route me-1 text-muted"></i>Parcours <span class="text-danger">*</span>
                                                    </label>
                                                    <select class="form-select <?php $__errorArgs = ['editUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            wire:model.defer="editUser.parcour_id">
                                                        <option value="">Choisir un parcours</option>
                                                        <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($parcour->id); ?>">
                                                                <?php echo e($parcour->sigle); ?> - <?php echo e($parcour->nom); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php $__errorArgs = ['editUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-layer-group me-1 text-muted"></i>Niveau <span class="text-danger">*</span>
                                                    </label>
                                                    <select class="form-select <?php $__errorArgs = ['editUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            wire:model.defer="editUser.niveau_id">
                                                        <option value="">Choisir un niveau</option>
                                                        <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php $__errorArgs = ['editUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-id-card-alt me-1 text-muted"></i>CIN
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.cin"
                                                           placeholder="Numéro CIN">
                                                    <?php $__errorArgs = ['editUser.cin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-copy me-1 text-muted"></i>Duplicata
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.duplicata'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.duplicata"
                                                           placeholder="Numéro duplicata">
                                                    <?php $__errorArgs = ['editUser.duplicata'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-calendar-check me-1 text-muted"></i>Date de délivrance
                                                    </label>
                                                    <input type="date" class="form-control <?php $__errorArgs = ['editUser.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.date_delivrance">
                                                    <?php $__errorArgs = ['editUser.date_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-map-marker-alt me-1 text-muted"></i>Lieu de délivrance
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.lieu_delivrance"
                                                           placeholder="Lieu de délivrance du CIN">
                                                    <?php $__errorArgs = ['editUser.lieu_delivrance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Family Information Card -->
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-users me-2"></i>Informations familiales
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                    
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-male me-1 text-muted"></i>Nom du père
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.nom_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.nom_pere"
                                                           placeholder="Nom complet du père">
                                                    <?php $__errorArgs = ['editUser.nom_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone du père
                                                    </label>
                                                    <input type="tel" class="form-control <?php $__errorArgs = ['editUser.tel_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.tel_pere"
                                                           placeholder="+261 XX XX XXX XX">
                                                    <?php $__errorArgs = ['editUser.tel_pere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-female me-1 text-muted"></i>Nom de la mère
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.nom_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.nom_mere"
                                                           placeholder="Nom complet de la mère">
                                                    <?php $__errorArgs = ['editUser.nom_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone de la mère
                                                    </label>
                                                    <input type="tel" class="form-control <?php $__errorArgs = ['editUser.tel_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.tel_mere"
                                                           placeholder="+261 XX XX XXX XX">
                                                    <?php $__errorArgs = ['editUser.tel_mere'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-user-tie me-1 text-muted"></i>Nom du tuteur
                                                    </label>
                                                    <input type="text" class="form-control <?php $__errorArgs = ['editUser.nom_tuteur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.nom_tuteur"
                                                           placeholder="Nom complet du tuteur (optionnel)">
                                                    <?php $__errorArgs = ['editUser.nom_tuteur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label fw-semibold">
                                                        <i class="fa fa-phone me-1 text-muted"></i>Téléphone du tuteur
                                                    </label>
                                                    <input type="tel" class="form-control <?php $__errorArgs = ['editUser.tel_tuteur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           wire:model.defer="editUser.tel_tuteur"
                                                           placeholder="+261 XX XX XXX XX">
                                                    <?php $__errorArgs = ['editUser.tel_tuteur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Payment History Section -->
                            <?php if(isset($payments) && count($payments) > 0): ?>
                                <div class="col-12 mt-4">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="mb-0">
                                                <i class="fa fa-credit-card me-2"></i>Historique des paiements
                                                <span class="badge bg-dark ms-2"><?php echo e(count($payments)); ?></span>
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-hover table-sm">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th><i class="fa fa-calendar me-1"></i>Date</th>
                                                            <th><i class="fa fa-money-bill me-1"></i>Montant</th>
                                                            <th><i class="fa fa-tag me-1"></i>Type</th>
                                                            <th><i class="fa fa-check-circle me-1"></i>État</th>
                                                            <th class="text-center"><i class="fa fa-cogs me-1"></i>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td>
                                                                    <small class="text-muted">
                                                                        <?php echo e($payment->created_at->format('d/m/Y H:i')); ?>

                                                                    </small>
                                                                </td>
                                                                <td>
                                                                    <strong class="text-success"><?php echo e(number_format($payment->montant, 0, ',', ' ')); ?> Ar</strong>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info"><?php echo e($payment->payment->nom ?? 'N/A'); ?></span>
                                                                </td>
                                                                <td>
                                                                    <?php if($payment->is_valid_sec): ?>
                                                                        <span class="badge bg-success">
                                                                            <i class="fa fa-check me-1"></i>Validé
                                                                        </span>
                                                                    <?php else: ?>
                                                                        <span class="badge bg-warning">
                                                                            <i class="fa fa-clock me-1"></i>En attente
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td class="text-center">
                                                                    <?php if(!$payment->is_valid_sec): ?>
                                                                        <button type="button" class="btn btn-sm btn-success btn-action"
                                                                                wire:click="validerPaiement(<?php echo e($payment->id); ?>)"
                                                                                title="Valider ce paiement">
                                                                            <i class="fa fa-check"></i>
                                                                        </button>
                                                                    <?php else: ?>
                                                                        <span class="text-muted">
                                                                            <i class="fa fa-check-circle"></i>
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </form>

                        <!-- Modal Footer with Enhanced Actions -->
                        <div class="modal-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="text-muted">
                                    <small>
                                        <i class="fa fa-info-circle me-1"></i>
                                        Les champs marqués d'un <span class="text-danger">*</span> sont obligatoires
                                    </small>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2"
                                            wire:click="closeAllModals">
                                        <i class="fa fa-times me-1"></i>Annuler
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-action"
                                            wire:click="updateUser"
                                            wire:loading.attr="disabled">
                                        <span wire:loading.remove wire:target="updateUser">
                                            <i class="fa fa-save me-1"></i>Enregistrer
                                        </span>
                                        <span wire:loading wire:target="updateUser">
                                            <i class="fa fa-spinner fa-spin me-1"></i>Enregistrement...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3 text-muted">Chargement des données de l'étudiant...</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Payment Modal -->
    <?php if($showPaymentModal): ?>
    <div class="modal fade show d-block" tabindex="-1" role="dialog" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title d-flex align-items-center">
                        <i class="fa fa-credit-card me-2"></i>
                        Historique des paiements - <strong class="ms-1"><?php echo e($selectedUserName); ?></strong>
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeAllModals" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?php if(count($payments) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th><i class="fa fa-calendar me-1"></i>Date</th>
                                        <th><i class="fa fa-money-bill me-1"></i>Montant</th>
                                        <th><i class="fa fa-tag me-1"></i>Type</th>
                                        <th><i class="fa fa-check-circle me-1"></i>État</th>
                                        <th class="text-center"><i class="fa fa-cogs me-1"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div><?php echo e($payment->created_at->format('d/m/Y')); ?></div>
                                                <small class="text-muted"><?php echo e($payment->created_at->format('H:i')); ?></small>
                                            </td>
                                            <td>
                                                <strong class="text-success fs-5"><?php echo e(number_format($payment->montant, 0, ',', ' ')); ?> Ar</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e($payment->payment->nom ?? 'N/A'); ?></span>
                                            </td>
                                            <td>
                                                <?php if($payment->is_valid_sec): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fa fa-check me-1"></i>Validé
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fa fa-clock me-1"></i>En attente
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php if(!$payment->is_valid_sec): ?>
                                                    <button type="button" class="btn btn-sm btn-success btn-action"
                                                            wire:click="validerPaiement(<?php echo e($payment->id); ?>)"
                                                            title="Valider ce paiement">
                                                        <i class="fa fa-check me-1"></i>Valider
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-success">
                                                        <i class="fa fa-check-circle fa-lg"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Payment Summary -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-primary"><?php echo e(count($payments)); ?></div>
                                    <small class="text-muted">Total paiements</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-success"><?php echo e($payments->where('is_valid_sec', 1)->count()); ?></div>
                                    <small class="text-muted">Validés</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="fs-4 fw-bold text-warning"><?php echo e($payments->where('is_valid_sec', 0)->count()); ?></div>
                                    <small class="text-muted">En attente</small>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun paiement enregistré</h5>
                            <p class="text-muted">Cet étudiant n'a effectué aucun paiement pour le moment.</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeAllModals">
                        <i class="fa fa-times me-1"></i>Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- New Student Modal -->
    <div wire:ignore.self class="modal fade" id="modal-new-student" tabindex="-1" role="dialog" aria-labelledby="modal-new-student" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Form will be added here for new student creation -->
                    <form wire:submit.prevent="createUser">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nom <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Téléphone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Niveau <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Année Universitaire <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">Sélectionner</option>
                                    <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-alt-secondary me-2" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-fw fa-plus me-1"></i> Créer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Enhanced notification system
        window.addEventListener("showSuccessMessage", event => {
            showNotification('success', event.detail.message || 'Opération effectuée avec succès!');
        });

        window.addEventListener("showErrorMessage", event => {
            showNotification('error', event.detail.message || 'Une erreur est survenue!');
        });

        // Modern notification function
        function showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Enhanced modal management
        window.addEventListener('closeModal', event => {
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show', 'd-block');
                modal.style.display = 'none';
            });
            document.body.classList.remove('modal-open');
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape key to close modals
            if (e.key === 'Escape') {
                window.livewire.find('<?php echo e($_instance->id); ?>').call('closeAllModals');
            }

            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.querySelector('input[wire\\:model*="query"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Auto-save user preferences
        window.addEventListener('compactViewToggled', event => {
            localStorage.setItem('infoetu_compact_view', event.detail.compact);
        });

        window.addEventListener('filtersToggled', event => {
            localStorage.setItem('infoetu_show_filters', event.detail.show);
        });

        // Restore user preferences
        const compactView = localStorage.getItem('infoetu_compact_view') === 'true';
        const showFilters = localStorage.getItem('infoetu_show_filters') !== 'false';

        if (compactView !== window.livewire.find('<?php echo e($_instance->id); ?>').compactView) {
            window.livewire.find('<?php echo e($_instance->id); ?>').set('compactView', compactView);
        }
        if (showFilters !== window.livewire.find('<?php echo e($_instance->id); ?>').showFilters) {
            window.livewire.find('<?php echo e($_instance->id); ?>').set('showFilters', showFilters);
        }

        // Enhanced table interactions
        document.addEventListener('click', function(e) {
            // Handle sortable headers
            if (e.target.closest('th[wire\\:click*="sortBy"]')) {
                const th = e.target.closest('th');
                th.style.opacity = '0.7';
                setTimeout(() => th.style.opacity = '1', 200);
            }
        });

        // Smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Mobile navigation helper
        function setupMobileNavigation() {
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // Add swipe gestures for mobile
                let startX = 0;
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                });

                document.addEventListener('touchend', function(e) {
                    if (!startX || !startY) return;

                    const endX = e.changedTouches[0].clientX;
                    const endY = e.changedTouches[0].clientY;

                    const diffX = startX - endX;
                    const diffY = startY - endY;

                    // Horizontal swipe
                    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                        if (diffX > 0) {
                            // Swipe left - could trigger next page
                            console.log('Swipe left detected');
                        } else {
                            // Swipe right - could trigger previous page
                            console.log('Swipe right detected');
                        }
                    }

                    startX = 0;
                    startY = 0;
                });
            }
        }

        setupMobileNavigation();

        // Responsive table helper
        function handleResponsiveTable() {
            const table = document.querySelector('.table-modern');
            const isMobile = window.innerWidth <= 576;

            if (table && isMobile) {
                table.classList.add('table-mobile-simple');
            } else if (table) {
                table.classList.remove('table-mobile-simple');
            }
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            handleResponsiveTable();
            setupMobileNavigation();
        });

        // Initial setup
        handleResponsiveTable();
    });

    // Refresh statistics function
    function refreshStats() {
        window.livewire.find('<?php echo e($_instance->id); ?>').call('loadStatistics');
        showNotification('success', 'Statistiques actualisées');
    }

    // Enhanced loading states
    document.addEventListener('livewire:load', function () {
        Livewire.hook('message.sent', (message, component) => {
            // Show loading state
            document.body.style.cursor = 'wait';
        });

        Livewire.hook('message.processed', (message, component) => {
            // Hide loading state
            document.body.style.cursor = 'default';
        });
    });
</script><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/secretaire/infoetu/index.blade.php ENDPATH**/ ?>