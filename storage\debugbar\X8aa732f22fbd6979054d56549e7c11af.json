{"__meta": {"id": "X8aa732f22fbd6979054d56549e7c11af", "datetime": "2025-07-22 09:59:47", "utime": 1753167587.652922, "method": "POST", "uri": "/livewire/message/info-etu", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753167586.79678, "end": 1753167587.652949, "duration": 0.8561689853668213, "duration_str": "856ms", "measures": [{"label": "Booting", "start": 1753167586.79678, "relative_start": 0, "end": 1753167587.303731, "relative_end": 1753167587.303731, "duration": 0.506950855255127, "duration_str": "507ms", "params": [], "collector": null}, {"label": "Application", "start": 1753167587.304567, "relative_start": 0.5077869892120361, "end": 1753167587.652952, "relative_end": 2.86102294921875e-06, "duration": 0.3483848571777344, "duration_str": "348ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27159424, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.secretaire.infoetu.index (\\resources\\views\\livewire\\secretaire\\infoetu\\index.blade.php)", "param_count": 18, "params": ["etus", "etuStatus", "totalEtuCount", "niveaux", "annees", "parcours", "livewireLayout", "errors", "_instance", "currentPage", "editUser", "query", "payments", "filtre<PERSON><PERSON>au", "filtreAnnee", "filtreRempli", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/secretaire/infoetu/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.04714999999999999, "accumulated_duration_str": "47.15ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00591, "duration_str": "5.91ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 12.534}, {"sql": "select * from `historique_payments` where `historique_payments`.`id` in (3589, 3590)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 12.534, "width_percent": 2.142}, {"sql": "select `id`, `nom`, `prenom` from `users` where `users`.`id` in (506) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 14.677, "width_percent": 1.697}, {"sql": "select * from `type_payments` where `type_payments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 16.373, "width_percent": 1.972}, {"sql": "select `parcours`.*, (select count(*) from `inscription_students` where `parcours`.`id` = `inscription_students`.`parcour_id` and `inscription_students`.`deleted_at` is null) as `etu_count` from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01079, "duration_str": "10.79ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:90", "connection": "imsaaapp", "start_percent": 18.346, "width_percent": 22.884}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01286, "duration_str": "12.86ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 41.23, "width_percent": 27.275}, {"sql": "select * from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null limit 25 offset 0", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00175, "duration_str": "1.75ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 68.505, "width_percent": 3.712}, {"sql": "select * from `users` where `users`.`id` in (4, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 27, 28, 29, 31, 34, 36, 37) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00186, "duration_str": "1.86ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 72.216, "width_percent": 3.945}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 3, 12) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 76.161, "width_percent": 2.1}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0035299999999999997, "duration_str": "3.53ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 78.261, "width_percent": 7.487}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0036, "duration_str": "3.6ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 85.748, "width_percent": 7.635}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 101}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:101", "connection": "imsaaapp", "start_percent": 93.383, "width_percent": 1.676}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 102}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:102", "connection": "imsaaapp", "start_percent": 95.058, "width_percent": 3.054}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 103}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:103", "connection": "imsaaapp", "start_percent": 98.112, "width_percent": 1.888}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 7, "App\\Models\\Niveau": 7, "App\\Models\\InscriptionStudent": 25, "App\\Models\\Parcour": 51, "App\\Models\\TypePayment": 2, "App\\Models\\HistoriquePayment": 2, "App\\Models\\User": 27}, "count": 121}, "livewire": {"data": {"info-etu #NLhYCPItDtRIRyDL3Ka2": "array:5 [\n  \"data\" => array:9 [\n    \"currentPage\" => \"liste\"\n    \"editUser\" => []\n    \"query\" => null\n    \"payments\" => Illuminate\\Database\\Eloquent\\Collection {#2113\n      #items: array:2 [\n        0 => App\\Models\\HistoriquePayment {#1706\n          #connection: \"mysql\"\n          #table: \"historique_payments\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 3589\n            \"user_id\" => 506\n            \"type_payment_id\" => 1\n            \"moyen_payment_id\" => 1\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 19\n            \"code\" => \"Reprehenderit et fug\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:57:48\"\n            \"updated_at\" => \"2025-07-22 09:57:48\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #original: array:14 [\n            \"id\" => 3589\n            \"user_id\" => 506\n            \"type_payment_id\" => 1\n            \"moyen_payment_id\" => 1\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 19\n            \"code\" => \"Reprehenderit et fug\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:57:48\"\n            \"updated_at\" => \"2025-07-22 09:57:48\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"user\" => App\\Models\\User {#1755\n              #connection: \"mysql\"\n              #table: \"users\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:3 [\n                \"id\" => 506\n                \"nom\" => \"Gillian\"\n                \"prenom\" => \"Robinson\"\n              ]\n              #original: array:3 [\n                \"id\" => 506\n                \"nom\" => \"Gillian\"\n                \"prenom\" => \"Robinson\"\n              ]\n              #changes: []\n              #casts: array:2 [\n                \"email_verified_at\" => \"datetime\"\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: array:2 [\n                0 => \"password\"\n                1 => \"remember_token\"\n              ]\n              #visible: []\n              #fillable: []\n              #guarded: []\n              #rememberTokenName: \"remember_token\"\n              #cascadeDeletes: array:3 [\n                0 => \"notes\"\n                1 => \"info\"\n                2 => \"historique\"\n              ]\n              #accessToken: null\n              #forceDeleting: false\n            }\n            \"payment\" => App\\Models\\TypePayment {#1717\n              #connection: \"mysql\"\n              #table: \"type_payments\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Droit d'inscription\"\n              ]\n              #original: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Droit d'inscription\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"nom\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:11 [\n            0 => \"user_id\"\n            1 => \"type_payment_id\"\n            2 => \"moyen_payment_id\"\n            3 => \"type_encaissement_id\"\n            4 => \"annee_universitaire_id\"\n            5 => \"montant\"\n            6 => \"libelle\"\n            7 => \"is_valid\"\n            8 => \"is_valid_sec\"\n            9 => \"code\"\n            10 => \"created_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\HistoriquePayment {#1708\n          #connection: \"mysql\"\n          #table: \"historique_payments\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 3590\n            \"user_id\" => 506\n            \"type_payment_id\" => 2\n            \"moyen_payment_id\" => 1\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 30\n            \"code\" => \"Dolorem suscipit cum\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:57:48\"\n            \"updated_at\" => \"2025-07-22 09:57:48\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #original: array:14 [\n            \"id\" => 3590\n            \"user_id\" => 506\n            \"type_payment_id\" => 2\n            \"moyen_payment_id\" => 1\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 30\n            \"code\" => \"Dolorem suscipit cum\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:57:48\"\n            \"updated_at\" => \"2025-07-22 09:57:48\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"user\" => App\\Models\\User {#1755}\n            \"payment\" => App\\Models\\TypePayment {#1786\n              #connection: \"mysql\"\n              #table: \"type_payments\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 2\n                \"nom\" => \"Fiche\"\n              ]\n              #original: array:2 [\n                \"id\" => 2\n                \"nom\" => \"Fiche\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"nom\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:11 [\n            0 => \"user_id\"\n            1 => \"type_payment_id\"\n            2 => \"moyen_payment_id\"\n            3 => \"type_encaissement_id\"\n            4 => \"annee_universitaire_id\"\n            5 => \"montant\"\n            6 => \"libelle\"\n            7 => \"is_valid\"\n            8 => \"is_valid_sec\"\n            9 => \"code\"\n            10 => \"created_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"filtreRempli\" => \"\"\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"info-etu\"\n  \"view\" => \"livewire.secretaire.infoetu.index\"\n  \"component\" => \"App\\Http\\Livewire\\InfoEtu\"\n  \"id\" => \"NLhYCPItDtRIRyDL3Ka2\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestion/inscription\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753167149\n]"}, "request": {"path_info": "/livewire/message/info-etu", "status_code": "<pre class=sf-dump id=sf-dump-745486696 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-745486696\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1838517450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1838517450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1132528998 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">NLhYCPItDtRIRyDL3Ka2</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">info-etu</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"19 characters\">gestion/inscription</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">5b3e1d59</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payments</span>\" => []\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreRempli</span>\" => \"<span class=sf-dump-str title=\"10 characters\">non_rempli</span>\"\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>payments</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\HistoriquePayment</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>3589</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>3590</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">payment</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">78bb08d36408eb0cef84cbfe62b9f1003ca3fc4d208eb751c709925de5607209</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mgdc</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">filtreRempli</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132528998\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-943209749 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">714</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpqSXMvZS9rbDljY3FxK2hJQ3MxL3c9PSIsInZhbHVlIjoiVW1ORTYza1N1VDlDd1JCTHM0NG9hb0p3NnJYZzdhSlduQTNrTWU4UElpbHQySzZCN0FOaitrcGhENGc5ajBTZThMN0VtNUhua2t0YjVPdk0vQmJIWTE1WEZueDlLVHpYcFhvaHlVaG1JeVlFc1B2MHdRUVROaGsrUG9ScjR1ZjEiLCJtYWMiOiI3OWRkNDI3YWY2MzczNGM2MTBiMmMzZDVlNWJmZTk3MjM1YjhhNGUzODZhYmNjZTg3MTY5NTUyZjBhZmUzNTY2IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Imcxa0hzc2ZYWnVZY0wyRUh0bGpabUE9PSIsInZhbHVlIjoiMkhjY1pERHk4ampMVEtRNHpKN3NYNWZTaHJwbXJieVEyVjkyZWtMT0N2V1lkdGpvTHFpRUUwajI3bmRWeDI3TTVaYloraGV0aXlEZTVGaUNzNjFEZVZFdEVKU0ZPS3lpcCs5YmM5TjZPSTBDa0tmcUJHN1NpcmptZnY3SGZpZTEiLCJtYWMiOiI0OGFjN2FkMzA0Y2IwZDVmYzM5MWFkNjU1MjkyZThjOWU5OTFjYzZhNGViODJmNjc4MmUwZWVkZDk5NDZjMjBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943209749\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1838156294 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56918</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">714</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">714</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpqSXMvZS9rbDljY3FxK2hJQ3MxL3c9PSIsInZhbHVlIjoiVW1ORTYza1N1VDlDd1JCTHM0NG9hb0p3NnJYZzdhSlduQTNrTWU4UElpbHQySzZCN0FOaitrcGhENGc5ajBTZThMN0VtNUhua2t0YjVPdk0vQmJIWTE1WEZueDlLVHpYcFhvaHlVaG1JeVlFc1B2MHdRUVROaGsrUG9ScjR1ZjEiLCJtYWMiOiI3OWRkNDI3YWY2MzczNGM2MTBiMmMzZDVlNWJmZTk3MjM1YjhhNGUzODZhYmNjZTg3MTY5NTUyZjBhZmUzNTY2IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Imcxa0hzc2ZYWnVZY0wyRUh0bGpabUE9PSIsInZhbHVlIjoiMkhjY1pERHk4ampMVEtRNHpKN3NYNWZTaHJwbXJieVEyVjkyZWtMT0N2V1lkdGpvTHFpRUUwajI3bmRWeDI3TTVaYloraGV0aXlEZTVGaUNzNjFEZVZFdEVKU0ZPS3lpcCs5YmM5TjZPSTBDa0tmcUJHN1NpcmptZnY3SGZpZTEiLCJtYWMiOiI0OGFjN2FkMzA0Y2IwZDVmYzM5MWFkNjU1MjkyZThjOWU5OTFjYzZhNGViODJmNjc4MmUwZWVkZDk5NDZjMjBlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753167586.7968</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753167586</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838156294\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2069331079 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wtgdgWdjyT0TX1yKLC0SW9faQdN6okxlgxE1VJm4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069331079\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-876498696 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 06:59:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJ1bWl0UFZqNytvc0pkMnk1ZzcyVmc9PSIsInZhbHVlIjoiOVVYNGRrbTdKdnMwa3N0a0kwN203d2pRWk50ZHBwa1h0dC95MEg3Z29EVDAzRTdQQWxCNWsyWVl2US9lM3V4TkE0YmJXalg4WnVrb3F6dkFMR0RrZWM5WVhWaGlVL1M5Z25GZTgwVWVPZTZ2ZVd1U0hJdVVBTnhCeXVacCtIc1kiLCJtYWMiOiIwZTZmZTNjNmJiMDg5OTViYjA1ZjY0YmVhNjExMzkzNTkyYTJhMTNlMjdlNDQ1MGVhMGM1NjQ4ZDQ3NGZjYzlhIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 08:59:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IjJzL0ozUVYxVXdoK3VzaFJYVnFabVE9PSIsInZhbHVlIjoiUFFMZ2hXdGM1dHYvYmdEU2ZOS2FuZ3JPRGdyZUQrZWdac3VXS3NHZVgxOFRCOGd4QVlOZjRJdituenE0UithS3FBa2VmcmZ3R3hlTm9DYUFEV2ZDUVlnb2oyWmpOUzZoLzY2MEw4L2dWRGI1clJsZC9PRGNXaUc4WEsvZkttZ2ciLCJtYWMiOiJiZmZmOGM3NGQ2YTNmYWJjOGRhMGY0Y2JmZTBjMDk3NzYyYzNlNWE1NmY4OThiMTI0YWRmYTVjNzRmYzAzYjdkIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 08:59:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJ1bWl0UFZqNytvc0pkMnk1ZzcyVmc9PSIsInZhbHVlIjoiOVVYNGRrbTdKdnMwa3N0a0kwN203d2pRWk50ZHBwa1h0dC95MEg3Z29EVDAzRTdQQWxCNWsyWVl2US9lM3V4TkE0YmJXalg4WnVrb3F6dkFMR0RrZWM5WVhWaGlVL1M5Z25GZTgwVWVPZTZ2ZVd1U0hJdVVBTnhCeXVacCtIc1kiLCJtYWMiOiIwZTZmZTNjNmJiMDg5OTViYjA1ZjY0YmVhNjExMzkzNTkyYTJhMTNlMjdlNDQ1MGVhMGM1NjQ4ZDQ3NGZjYzlhIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 08:59:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IjJzL0ozUVYxVXdoK3VzaFJYVnFabVE9PSIsInZhbHVlIjoiUFFMZ2hXdGM1dHYvYmdEU2ZOS2FuZ3JPRGdyZUQrZWdac3VXS3NHZVgxOFRCOGd4QVlOZjRJdituenE0UithS3FBa2VmcmZ3R3hlTm9DYUFEV2ZDUVlnb2oyWmpOUzZoLzY2MEw4L2dWRGI1clJsZC9PRGNXaUc4WEsvZkttZ2ciLCJtYWMiOiJiZmZmOGM3NGQ2YTNmYWJjOGRhMGY0Y2JmZTBjMDk3NzYyYzNlNWE1NmY4OThiMTI0YWRmYTVjNzRmYzAzYjdkIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 08:59:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876498696\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1302204008 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753167149</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302204008\", {\"maxDepth\":0})</script>\n"}}