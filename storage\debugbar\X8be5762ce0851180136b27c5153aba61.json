{"__meta": {"id": "X8be5762ce0851180136b27c5153aba61", "datetime": "2025-07-22 09:57:48", "utime": 1753167468.61617, "method": "POST", "uri": "/livewire/message/inscription-field", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753167467.417993, "end": 1753167468.61621, "duration": 1.1982169151306152, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1753167467.417993, "relative_start": 0, "end": 1753167468.02668, "relative_end": 1753167468.02668, "duration": 0.6086869239807129, "duration_str": "609ms", "params": [], "collector": null}, {"label": "Application", "start": 1753167468.027643, "relative_start": 0.6096498966217041, "end": 1753167468.616214, "relative_end": 4.0531158447265625e-06, "duration": 0.5885710716247559, "duration_str": "589ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26957752, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.caf.inscription.index (\\resources\\views\\livewire\\caf\\inscription\\index.blade.php)", "param_count": 8, "params": ["niveaux", "moyens", "annees", "livewireLayout", "errors", "_instance", "currentPage", "newUser"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/caf/inscription/index.blade.php&line=0"}, {"name": "livewire.caf.inscription.create (\\resources\\views\\livewire\\caf\\inscription\\create.blade.php)", "param_count": 10, "params": ["__env", "app", "errors", "_instance", "niveaux", "moyens", "annees", "livewireLayout", "currentPage", "newUser"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/caf/inscription/create.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.11091000000000001, "accumulated_duration_str": "111ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00651, "duration_str": "6.51ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 5.87}, {"sql": "insert into `users` (`nom`, `prenom`, `niveau_id`, `updated_at`, `created_at`) values ('<PERSON>', '<PERSON>', '2', '2025-07-22 09:57:48', '2025-07-22 09:57:48')", "type": "query", "params": [], "bindings": ["<PERSON>", "<PERSON>", "2", "2025-07-22 09:57:48", "2025-07-22 09:57:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 56}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04111, "duration_str": "41.11ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:56", "connection": "imsaaapp", "start_percent": 5.87, "width_percent": 37.066}, {"sql": "insert into `role_user` (`role_id`, `user_id`) values (5, 506)", "type": "query", "params": [], "bindings": ["5", "506"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 62}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01638, "duration_str": "16.38ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:62", "connection": "imsaaapp", "start_percent": 42.936, "width_percent": 14.769}, {"sql": "insert into `inscription_students` (`annee_universitaire_id`, `niveau_id`, `user_id`, `updated_at`, `created_at`) values ('6', '2', 506, '2025-07-22 09:57:48', '2025-07-22 09:57:48')", "type": "query", "params": [], "bindings": ["6", "2", "506", "2025-07-22 09:57:48", "2025-07-22 09:57:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 64}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00908, "duration_str": "9.08ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:64", "connection": "imsaaapp", "start_percent": 57.704, "width_percent": 8.187}, {"sql": "insert into `historique_payments` (`moyen_payment_id`, `montant`, `code`, `type_encaissement_id`, `annee_universitaire_id`, `type_payment_id`, `user_id`, `updated_at`, `created_at`) values ('1', '19', 'Reprehenderit et fug', 1, '6', 1, 506, '2025-07-22 09:57:48', '2025-07-22 09:57:48')", "type": "query", "params": [], "bindings": ["1", "19", "Reprehenderit et fug", "1", "6", "1", "506", "2025-07-22 09:57:48", "2025-07-22 09:57:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 69}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.02253, "duration_str": "22.53ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:69", "connection": "imsaaapp", "start_percent": 65.891, "width_percent": 20.314}, {"sql": "insert into `historique_payments` (`moyen_payment_id`, `montant`, `code`, `type_encaissement_id`, `annee_universitaire_id`, `type_payment_id`, `user_id`, `updated_at`, `created_at`) values ('1', '30', 'Dolorem suscipit cum', 1, '6', 2, 506, '2025-07-22 09:57:48', '2025-07-22 09:57:48')", "type": "query", "params": [], "bindings": ["1", "30", "Dolorem suscipit cum", "1", "6", "2", "506", "2025-07-22 09:57:48", "2025-07-22 09:57:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 78}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.006900000000000001, "duration_str": "6.9ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:78", "connection": "imsaaapp", "start_percent": 86.205, "width_percent": 6.221}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 22}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:22", "connection": "imsaaapp", "start_percent": 92.426, "width_percent": 1.641}, {"sql": "select * from `moyen_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 23}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:23", "connection": "imsaaapp", "start_percent": 94.067, "width_percent": 0.748}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InscriptionField.php", "line": 24}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00575, "duration_str": "5.75ms", "stmt_id": "\\app\\Http\\Livewire\\InscriptionField.php:24", "connection": "imsaaapp", "start_percent": 94.816, "width_percent": 5.184}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 6, "App\\Models\\MoyenPayment": 3, "App\\Models\\Niveau": 5, "App\\Models\\User": 1}, "count": 15}, "livewire": {"data": {"inscription-field #e1qWzobLyhaVyrMRdwbl": "array:5 [\n  \"data\" => array:2 [\n    \"currentPage\" => \"create\"\n    \"newUser\" => []\n  ]\n  \"name\" => \"inscription-field\"\n  \"view\" => \"livewire.caf.inscription.index\"\n  \"component\" => \"App\\Http\\Livewire\\InscriptionField\"\n  \"id\" => \"e1qWzobLyhaVyrMRdwbl\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestion/inscription\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753167149\n]"}, "request": {"path_info": "/livewire/message/inscription-field", "status_code": "<pre class=sf-dump id=sf-dump-540781873 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-540781873\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-254971439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254971439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1193792114 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">e1qWzobLyhaVyrMRdwbl</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">inscription-field</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">caisse/inscription</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">c628b120</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      \"<span class=sf-dump-key>newUser</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>annee</span>\" => \"<span class=sf-dump-str>6</span>\"\n        \"<span class=sf-dump-key>moyen</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Gillian</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Robinson</span>\"\n        \"<span class=sf-dump-key>droitcode</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Reprehenderit et fug</span>\"\n        \"<span class=sf-dump-key>droit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">19</span>\"\n        \"<span class=sf-dump-key>fichecode</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolorem suscipit cum</span>\"\n        \"<span class=sf-dump-key>fiche</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">6ed21ede6635087a921252f9923086c8001309f8c2406941a32257426a7a2e40</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">hqzs</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">addUser</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193792114\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1615606275 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">599</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/caisse/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikl2UjM1MmgrVW1lRkJsTUloR3phWkE9PSIsInZhbHVlIjoiQlNQT0ZNVTk3OU9hUW9pV3BlNWhzZllCaHMwRmRDUnZrVldXVTRwbXRGYm5XL0tBUi9Uc2RUQ2lTQUUwZFIzWXpPSjBlWlBsVDZOalkxYklIaEMvSUFpZEg1UmVyZjlEVFRBOE9jN0VZK3Zwc1c4YnRNdjlZREJiaFFuWnoyMVoiLCJtYWMiOiI0OTE5MGZjMzQyZDRkNjdlMWYxZTRlZmY0YmU5YzY4NmJmZGVkYzliODhhNDFmNjYzMTkwMWM4MWUzYzc2MjkwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImFLb29EdUp2VWZZRTlPc0V4NHYvMFE9PSIsInZhbHVlIjoiVm10YnRPazFna3hmZFNmbzF4b0xpN2VLNjZhdnpQZTJqK1VGUGlNMDVmdWo2ZllvRTBPelVmTXFDVjVEZzNtNHVrSTBTZGdkL2sxRlpCQXJuSGJidDdQcVQ1WmZoakZId3J2OFk1cW9Zc3YrQStBZnJIOEhzSTB4b01nS0lITFAiLCJtYWMiOiI3Y2M3MGUzY2NkYjk1MTA3MWZlYzdiNTc1NWIxYzNhYzhhOTBiMTRjNzgzNGRlOWVjZGI4ZGYxZjRjMDcwMzgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615606275\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-754396069 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56772</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/livewire/message/inscription-field</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/livewire/message/inscription-field</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"45 characters\">/index.php/livewire/message/inscription-field</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">599</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">599</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/caisse/inscription</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikl2UjM1MmgrVW1lRkJsTUloR3phWkE9PSIsInZhbHVlIjoiQlNQT0ZNVTk3OU9hUW9pV3BlNWhzZllCaHMwRmRDUnZrVldXVTRwbXRGYm5XL0tBUi9Uc2RUQ2lTQUUwZFIzWXpPSjBlWlBsVDZOalkxYklIaEMvSUFpZEg1UmVyZjlEVFRBOE9jN0VZK3Zwc1c4YnRNdjlZREJiaFFuWnoyMVoiLCJtYWMiOiI0OTE5MGZjMzQyZDRkNjdlMWYxZTRlZmY0YmU5YzY4NmJmZGVkYzliODhhNDFmNjYzMTkwMWM4MWUzYzc2MjkwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImFLb29EdUp2VWZZRTlPc0V4NHYvMFE9PSIsInZhbHVlIjoiVm10YnRPazFna3hmZFNmbzF4b0xpN2VLNjZhdnpQZTJqK1VGUGlNMDVmdWo2ZllvRTBPelVmTXFDVjVEZzNtNHVrSTBTZGdkL2sxRlpCQXJuSGJidDdQcVQ1WmZoakZId3J2OFk1cW9Zc3YrQStBZnJIOEhzSTB4b01nS0lITFAiLCJtYWMiOiI3Y2M3MGUzY2NkYjk1MTA3MWZlYzdiNTc1NWIxYzNhYzhhOTBiMTRjNzgzNGRlOWVjZGI4ZGYxZjRjMDcwMzgyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753167467.418</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753167467</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-754396069\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1758941144 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wtgdgWdjyT0TX1yKLC0SW9faQdN6okxlgxE1VJm4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758941144\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 06:57:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldDYmxJZlpvTXBtSVQ4bUdyMmV6RFE9PSIsInZhbHVlIjoiTGpHYVpYNnpITTh1eDN1UnNjWStXUFcvRXNYb1JJdFV1L1NMQzJuMzZqR1FxMmRiZXZGMjhENEpNZ2FELzVqQk4zT3NENjFvZVphOWNXRWVydUYyWWpWUFB6ekNaNmk4azRJVDhpQlpybEM4UUpuZWt2UHRyZWRRZ2ZtSmFnWUsiLCJtYWMiOiI0MTBjOGQ5ZDE4N2FiZTJkMGU3NGYxNDZjY2EzODZmNjUzZTAyN2VjODZjZTI5OGEzZWVhMzJhMDFiMmM3MjE4IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 08:57:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImNIVlpaQ1p4cEN3ZVA3OXNCang2V0E9PSIsInZhbHVlIjoiekVyRUtPLzBwRmd1Y01QeUx5T3JxTjBFalZCemdJaUErUHZyM1lRZURUNDZQN0RKTTB2TFRLRjlpdENyY2R3K2xkRmFITDA1M1R0MzlsTlpKM0xLZSs1OU0zR2o2WUFpWnRhYkYxemp3U3MvOXVaYjhNdGRoaEFIcE9YQmpCVW4iLCJtYWMiOiI1ODE5ZDZiNWRjOTBiODNhM2YxOTVjOTY4MDAyNWU0NjA2MTJhMzdkZDhhZDU4MzNmZWQ2Y2IxYmNiNTQ5YTU5IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 08:57:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldDYmxJZlpvTXBtSVQ4bUdyMmV6RFE9PSIsInZhbHVlIjoiTGpHYVpYNnpITTh1eDN1UnNjWStXUFcvRXNYb1JJdFV1L1NMQzJuMzZqR1FxMmRiZXZGMjhENEpNZ2FELzVqQk4zT3NENjFvZVphOWNXRWVydUYyWWpWUFB6ekNaNmk4azRJVDhpQlpybEM4UUpuZWt2UHRyZWRRZ2ZtSmFnWUsiLCJtYWMiOiI0MTBjOGQ5ZDE4N2FiZTJkMGU3NGYxNDZjY2EzODZmNjUzZTAyN2VjODZjZTI5OGEzZWVhMzJhMDFiMmM3MjE4IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 08:57:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImNIVlpaQ1p4cEN3ZVA3OXNCang2V0E9PSIsInZhbHVlIjoiekVyRUtPLzBwRmd1Y01QeUx5T3JxTjBFalZCemdJaUErUHZyM1lRZURUNDZQN0RKTTB2TFRLRjlpdENyY2R3K2xkRmFITDA1M1R0MzlsTlpKM0xLZSs1OU0zR2o2WUFpWnRhYkYxemp3U3MvOXVaYjhNdGRoaEFIcE9YQmpCVW4iLCJtYWMiOiI1ODE5ZDZiNWRjOTBiODNhM2YxOTVjOTY4MDAyNWU0NjA2MTJhMzdkZDhhZDU4MzNmZWQ2Y2IxYmNiNTQ5YTU5IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 08:57:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1946233451 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753167149</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946233451\", {\"maxDepth\":0})</script>\n"}}