{"__meta": {"id": "X35564c96a93554fb9446aecf4e2f9472", "datetime": "2025-07-22 11:30:04", "utime": 1753173004.052687, "method": "POST", "uri": "/livewire/message/info-etu", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753173001.972528, "end": 1753173004.052725, "duration": 2.0801970958709717, "duration_str": "2.08s", "measures": [{"label": "Booting", "start": 1753173001.972528, "relative_start": 0, "end": 1753173003.344952, "relative_end": 1753173003.344952, "duration": 1.3724241256713867, "duration_str": "1.37s", "params": [], "collector": null}, {"label": "Application", "start": 1753173003.346794, "relative_start": 1.3742659091949463, "end": 1753173004.052728, "relative_end": 2.86102294921875e-06, "duration": 0.7059340476989746, "duration_str": "706ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27384432, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.secretaire.infoetu.index (\\resources\\views\\livewire\\secretaire\\infoetu\\index.blade.php)", "param_count": 30, "params": ["etus", "etuStatus", "niveaux", "annees", "parcours", "livewireLayout", "errors", "_instance", "showEditModal", "showPaymentModal", "showDeleteModal", "editUser", "selectedUserId", "selectedUserName", "query", "payments", "filtre<PERSON><PERSON>au", "filtreAnnee", "filtreRempli", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalRemplis", "totalNonRemplis", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/secretaire/infoetu/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.04564999999999999, "accumulated_duration_str": "45.65ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00622, "duration_str": "6.22ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 13.625}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 103}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 76}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.00908, "duration_str": "9.08ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:103", "connection": "imsaaapp", "start_percent": 13.625, "width_percent": 19.89}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `parcour_id` is not null and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 76}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.009529999999999999, "duration_str": "9.53ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:104", "connection": "imsaaapp", "start_percent": 33.516, "width_percent": 20.876}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `parcour_id` is null and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 105}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 76}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:105", "connection": "imsaaapp", "start_percent": 54.392, "width_percent": 3.045}, {"sql": "select `parcours`.*, (select count(*) from `inscription_students` where `parcours`.`id` = `inscription_students`.`parcour_id` and exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `niveau_id` = '1' and `inscription_students`.`deleted_at` is null) as `etu_count` from `parcours` where `parcours`.`deleted_at` is null order by `nom` asc", "type": "query", "params": [], "bindings": ["5", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 166}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.007690000000000001, "duration_str": "7.69ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:166", "connection": "imsaaapp", "start_percent": 57.437, "width_percent": 16.846}, {"sql": "select count(*) as aggregate from `inscription_students` where `niveau_id` = '1' and exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00409, "duration_str": "4.09ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 74.283, "width_percent": 8.959}, {"sql": "select * from `inscription_students` where `niveau_id` = '1' and exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 25 offset 0", "type": "query", "params": [], "bindings": ["1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 83.242, "width_percent": 8.368}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo`, `email`, `matricule`, `date_naissance`, `lieu_naissance`, `adresse`, `cin` from `users` where `users`.`id` in (471, 472, 473, 475, 476, 478, 481, 482, 483, 484, 485, 486, 487, 489, 490, 492, 493, 495, 496, 497, 499, 506, 507, 508, 509) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 91.61, "width_percent": 2.87}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 7, 8, 22, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 94.48, "width_percent": 2.037}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 96.517, "width_percent": 1.709}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 170}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:170", "connection": "imsaaapp", "start_percent": 98.226, "width_percent": 1.774}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 1, "App\\Models\\InscriptionStudent": 25, "App\\Models\\Parcour": 33, "App\\Models\\User": 26}, "count": 86}, "livewire": {"data": {"info-etu #bSTNRyzslBOci5RcZEWh": "array:5 [\n  \"data\" => array:22 [\n    \"showEditModal\" => false\n    \"showPaymentModal\" => false\n    \"showDeleteModal\" => false\n    \"editUser\" => []\n    \"selectedUserId\" => null\n    \"selectedUserName\" => \"\"\n    \"query\" => \"\"\n    \"payments\" => []\n    \"filtreNiveau\" => \"1\"\n    \"filtreAnnee\" => \"\"\n    \"filtreRempli\" => \"\"\n    \"perPage\" => 25\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"totalEtudiants\" => 724\n    \"totalRemplis\" => 723\n    \"totalNonRemplis\" => 1\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"info-etu\"\n  \"view\" => \"livewire.secretaire.infoetu.index\"\n  \"component\" => \"App\\Http\\Livewire\\InfoEtu\"\n  \"id\" => \"bSTNRyzslBOci5RcZEWh\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestion/inscription\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753167149\n]"}, "request": {"path_info": "/livewire/message/info-etu", "status_code": "<pre class=sf-dump id=sf-dump-1145710172 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1145710172\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1000407914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1000407914\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1677961129 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">bSTNRyzslBOci5RcZEWh</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">info-etu</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"19 characters\">gestion/inscription</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">fd8521c9</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:22</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showPaymentModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserName</span>\" => \"\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>payments</span>\" => []\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => \"\"\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>filtreRempli</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>25</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>724</span>\n      \"<span class=sf-dump-key>totalRemplis</span>\" => <span class=sf-dump-num>723</span>\n      \"<span class=sf-dump-key>totalNonRemplis</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">fcbeb6773ca34c49a9c6fffe775fa4c0d94bfd0efc81d6f32503894f13d55819</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">9lshj</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">filtreNiveau</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677961129\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-760780797 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">805</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InF5cVZZOVhUQnBwSzhjZWdwQm8yR2c9PSIsInZhbHVlIjoieWZKdTlOUVRnQWZxTDFyVzZTL2RoWTZHVXFJYjdYQUt2am8wZHZFZ2VXQUgzeDBWTktkdnZ6OTYzYk9Mdk1MQzgyTi81ME9RSXg3VFl1ajlrUzdUbForbE1IeUJFMFRKN09kYVRjZzhiTG8rQVhHSVk4UGNTOVUycm9tUWdrbnkiLCJtYWMiOiIyMDYxOGRiOTg3N2VmZjNlNzg1MWIyNzA2ZmFlMzc2NzdjMTRkYTJkYzViZWY4NmYwYWZhY2FjOTU2NGVkZjAyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IkZjR2ZPbnRFdmJzc2Y5NU1MSkRVRGc9PSIsInZhbHVlIjoiSjY5Z1lDSkFIdUszWCtqK0UxYWhoVDBUOFcvMHBVZGZQL3h3L0VoWVZtZnVlUWxHcHNUQmx2NUp2UmdzV3hyTVJpa0hMcWtJZ0R0SkNlUFA3cHZyL3YxQmtPWkg3cjRvR1hhMWRzQmw4T3dxNGtwYXYzaTNtK2g0Z1hBVkpKVUsiLCJtYWMiOiIzZjkwYzQwYTE0NDM5Yjg3YTBhOWZkZmI1NTI3YWJjYTllNTU4YzU4NzAwZTQ3MThhNDJhMmE3MmExZmYyNGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760780797\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1197207008 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64618</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">805</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">805</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InF5cVZZOVhUQnBwSzhjZWdwQm8yR2c9PSIsInZhbHVlIjoieWZKdTlOUVRnQWZxTDFyVzZTL2RoWTZHVXFJYjdYQUt2am8wZHZFZ2VXQUgzeDBWTktkdnZ6OTYzYk9Mdk1MQzgyTi81ME9RSXg3VFl1ajlrUzdUbForbE1IeUJFMFRKN09kYVRjZzhiTG8rQVhHSVk4UGNTOVUycm9tUWdrbnkiLCJtYWMiOiIyMDYxOGRiOTg3N2VmZjNlNzg1MWIyNzA2ZmFlMzc2NzdjMTRkYTJkYzViZWY4NmYwYWZhY2FjOTU2NGVkZjAyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IkZjR2ZPbnRFdmJzc2Y5NU1MSkRVRGc9PSIsInZhbHVlIjoiSjY5Z1lDSkFIdUszWCtqK0UxYWhoVDBUOFcvMHBVZGZQL3h3L0VoWVZtZnVlUWxHcHNUQmx2NUp2UmdzV3hyTVJpa0hMcWtJZ0R0SkNlUFA3cHZyL3YxQmtPWkg3cjRvR1hhMWRzQmw4T3dxNGtwYXYzaTNtK2g0Z1hBVkpKVUsiLCJtYWMiOiIzZjkwYzQwYTE0NDM5Yjg3YTBhOWZkZmI1NTI3YWJjYTllNTU4YzU4NzAwZTQ3MThhNDJhMmE3MmExZmYyNGU2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753173001.9725</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753173001</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197207008\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1447618797 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wtgdgWdjyT0TX1yKLC0SW9faQdN6okxlgxE1VJm4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447618797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-389013057 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 08:30:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhDOWJmbU9jNHp3WHhUODJyUHFPTUE9PSIsInZhbHVlIjoiNUlpUmM2OSt6WUN1SFRaeWVJbTVhbno2TjRIVGRBdnY4Sk54cGYrVHRYRE1tUVI2Q3FxcGsvUFNCNmFteUlNOHZ0ZTg0TEJtd05Da3VQeXY3TU82dXhKWHpuQllIRWdMdWR5OFVBcUZqVlcvSmNwc29zbENKUGtrcGUzM0MrUC8iLCJtYWMiOiIwYjQzNjFmMjcwNjg2ZGU3ZWJkMTZkZTk4YWNiOTZhYjc1OWQyYTA0MDFlMWQ1NzZhMDhkZGE2YjAwNDliMjIzIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 10:30:03 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6InYzTlpHZGdoZ3ZnNFdnVUxMMk9jSkE9PSIsInZhbHVlIjoiQkE1b09RWitKVlBEZFZQN0FsU0xrcWFkQzMvcHlsWGVlZUN3ZEc1ajZLT3BTQXEvWWZaYnhSTXlyemprWDN6R1JKVjVHbnZ2cklYaUt2SVBIVmZXMjBMai9xK1VpM1VjZkV6ZjVTQ1R6ZmtvNGl6STlhcllQSEZzV20waVhpZzciLCJtYWMiOiI0MjFhZDYzNWQzNmU3NmY2NzFhMDcxYTczYzI1ZWNhMWNiMzE1NGUxOGRiMjFiOTc4ZDljNzVmMDNiOTkxNTA1IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 10:30:03 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhDOWJmbU9jNHp3WHhUODJyUHFPTUE9PSIsInZhbHVlIjoiNUlpUmM2OSt6WUN1SFRaeWVJbTVhbno2TjRIVGRBdnY4Sk54cGYrVHRYRE1tUVI2Q3FxcGsvUFNCNmFteUlNOHZ0ZTg0TEJtd05Da3VQeXY3TU82dXhKWHpuQllIRWdMdWR5OFVBcUZqVlcvSmNwc29zbENKUGtrcGUzM0MrUC8iLCJtYWMiOiIwYjQzNjFmMjcwNjg2ZGU3ZWJkMTZkZTk4YWNiOTZhYjc1OWQyYTA0MDFlMWQ1NzZhMDhkZGE2YjAwNDliMjIzIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 10:30:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6InYzTlpHZGdoZ3ZnNFdnVUxMMk9jSkE9PSIsInZhbHVlIjoiQkE1b09RWitKVlBEZFZQN0FsU0xrcWFkQzMvcHlsWGVlZUN3ZEc1ajZLT3BTQXEvWWZaYnhSTXlyemprWDN6R1JKVjVHbnZ2cklYaUt2SVBIVmZXMjBMai9xK1VpM1VjZkV6ZjVTQ1R6ZmtvNGl6STlhcllQSEZzV20waVhpZzciLCJtYWMiOiI0MjFhZDYzNWQzNmU3NmY2NzFhMDcxYTczYzI1ZWNhMWNiMzE1NGUxOGRiMjFiOTc4ZDljNzVmMDNiOTkxNTA1IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 10:30:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389013057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-214510391 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753167149</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214510391\", {\"maxDepth\":0})</script>\n"}}