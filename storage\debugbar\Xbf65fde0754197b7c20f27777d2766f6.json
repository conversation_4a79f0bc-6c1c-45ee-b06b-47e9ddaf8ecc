{"__meta": {"id": "Xbf65fde0754197b7c20f27777d2766f6", "datetime": "2025-07-22 10:00:09", "utime": 1753167609.637718, "method": "POST", "uri": "/livewire/message/info-etu", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753167607.195045, "end": 1753167609.637748, "duration": 2.4427030086517334, "duration_str": "2.44s", "measures": [{"label": "Booting", "start": 1753167607.195045, "relative_start": 0, "end": 1753167608.126425, "relative_end": 1753167608.126425, "duration": 0.931380033493042, "duration_str": "931ms", "params": [], "collector": null}, {"label": "Application", "start": 1753167608.127505, "relative_start": 0.9324600696563721, "end": 1753167609.637751, "relative_end": 3.0994415283203125e-06, "duration": 1.5102460384368896, "duration_str": "1.51s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27128824, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.secretaire.infoetu.index (\\resources\\views\\livewire\\secretaire\\infoetu\\index.blade.php)", "param_count": 18, "params": ["etus", "etuStatus", "totalEtuCount", "niveaux", "annees", "parcours", "livewireLayout", "errors", "_instance", "currentPage", "editUser", "query", "payments", "filtre<PERSON><PERSON>au", "filtreAnnee", "filtreRempli", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/secretaire/infoetu/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 18, "nb_failed_statements": 0, "accumulated_duration": 1.01769, "accumulated_duration_str": "1.02s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.009720000000000001, "duration_str": "9.72ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.955}, {"sql": "select * from `historique_payments` where `historique_payments`.`id` in (3589, 3590)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00604, "duration_str": "6.04ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 0.955, "width_percent": 0.594}, {"sql": "select `id`, `nom`, `prenom` from `users` where `users`.`id` in (506) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00209, "duration_str": "2.09ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 1.549, "width_percent": 0.205}, {"sql": "select * from `type_payments` where `type_payments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 1.754, "width_percent": 0.078}, {"sql": "select * from `users` where `users`.`id` = 507 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["507"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 173}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.004690000000000001, "duration_str": "4.69ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:173", "connection": "imsaaapp", "start_percent": 1.832, "width_percent": 0.461}, {"sql": "select * from `historique_payments` where `user_id` = 507 and (`type_payment_id` = 1 or `type_payment_id` = 2) and `historique_payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["507", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 158}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 175}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:158", "connection": "imsaaapp", "start_percent": 2.292, "width_percent": 0.113}, {"sql": "select `id`, `nom`, `prenom` from `users` where `users`.`id` in (507) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 158}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 175}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.019370000000000002, "duration_str": "19.37ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:158", "connection": "imsaaapp", "start_percent": 2.405, "width_percent": 1.903}, {"sql": "select * from `type_payments` where `type_payments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 158}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 175}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.046770000000000006, "duration_str": "46.77ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:158", "connection": "imsaaapp", "start_percent": 4.309, "width_percent": 4.596}, {"sql": "select `parcours`.*, (select count(*) from `inscription_students` where `parcours`.`id` = `inscription_students`.`parcour_id` and `annee_universitaire_id` = '6' and `inscription_students`.`deleted_at` is null) as `etu_count` from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04747, "duration_str": "47.47ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:90", "connection": "imsaaapp", "start_percent": 8.904, "width_percent": 4.664}, {"sql": "select count(*) as aggregate from `inscription_students` where `annee_universitaire_id` = '6' and exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.08632, "duration_str": "86.32ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 13.569, "width_percent": 8.482}, {"sql": "select * from `inscription_students` where `annee_universitaire_id` = '6' and exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null limit 25 offset 225", "type": "query", "params": [], "bindings": ["6", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.10993, "duration_str": "110ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 22.051, "width_percent": 10.802}, {"sql": "select * from `users` where `users`.`id` in (503, 504, 505, 506, 507) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.10284, "duration_str": "103ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 32.853, "width_percent": 10.105}, {"sql": "select * from `parcours` where `parcours`.`id` in (5, 22) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.11981, "duration_str": "120ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 42.958, "width_percent": 11.773}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 4) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.10492, "duration_str": "105ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 54.731, "width_percent": 10.31}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.13421, "duration_str": "134ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:97", "connection": "imsaaapp", "start_percent": 65.04, "width_percent": 13.188}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 101}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.11151000000000001, "duration_str": "112ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:101", "connection": "imsaaapp", "start_percent": 78.228, "width_percent": 10.957}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 102}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.05757, "duration_str": "57.57ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:102", "connection": "imsaaapp", "start_percent": 89.185, "width_percent": 5.657}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\InfoEtu.php", "line": 103}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.05249, "duration_str": "52.49ms", "stmt_id": "\\app\\Http\\Livewire\\InfoEtu.php:103", "connection": "imsaaapp", "start_percent": 94.842, "width_percent": 5.158}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 7, "App\\Models\\Niveau": 7, "App\\Models\\InscriptionStudent": 5, "App\\Models\\Parcour": 50, "App\\Models\\TypePayment": 4, "App\\Models\\HistoriquePayment": 4, "App\\Models\\User": 9}, "count": 86}, "livewire": {"data": {"info-etu #NLhYCPItDtRIRyDL3Ka2": "array:5 [\n  \"data\" => array:9 [\n    \"currentPage\" => \"edit\"\n    \"editUser\" => array:32 [\n      \"id\" => 507\n      \"nom\" => \"Cecilia\"\n      \"prenom\" => \"Alvarado\"\n      \"sexe\" => \"H\"\n      \"date_naissance\" => \"26/02/81\"\n      \"lieu_naissance\" => \"AZE\"\n      \"nationalite\" => null\n      \"ville\" => null\n      \"pays\" => null\n      \"adresse\" => null\n      \"telephone1\" => \"2314\"\n      \"telephone2\" => null\n      \"nom_pere\" => null\n      \"nom_mere\" => null\n      \"cin\" => null\n      \"date_delivrance\" => null\n      \"lieu_delivrance\" => null\n      \"duplicata\" => null\n      \"matricule\" => \"001/25/IMSAA\"\n      \"email\" => null\n      \"photo\" => \"media/avatars/avatar0.jpg\"\n      \"inscription_date\" => \"22-07-2025\"\n      \"parcour_id\" => 5\n      \"niveau_id\" => 1\n      \"created_at\" => \"2025-07-22T06:58:22.000000Z\"\n      \"updated_at\" => \"2025-07-22T06:59:16.000000Z\"\n      \"deleted_at\" => null\n      \"is_filled\" => 1\n      \"tel_pere\" => null\n      \"tel_mere\" => null\n      \"nom_tuteur\" => null\n      \"tel_tuteur\" => null\n    ]\n    \"query\" => null\n    \"payments\" => Illuminate\\Database\\Eloquent\\Collection {#1904\n      #items: array:2 [\n        0 => App\\Models\\HistoriquePayment {#2227\n          #connection: \"mysql\"\n          #table: \"historique_payments\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 3591\n            \"user_id\" => 507\n            \"type_payment_id\" => 1\n            \"moyen_payment_id\" => 2\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 53\n            \"code\" => \"Beatae ex consequat\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:58:22\"\n            \"updated_at\" => \"2025-07-22 09:58:22\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #original: array:14 [\n            \"id\" => 3591\n            \"user_id\" => 507\n            \"type_payment_id\" => 1\n            \"moyen_payment_id\" => 2\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 53\n            \"code\" => \"Beatae ex consequat\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:58:22\"\n            \"updated_at\" => \"2025-07-22 09:58:22\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"user\" => App\\Models\\User {#2199\n              #connection: \"mysql\"\n              #table: \"users\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:3 [\n                \"id\" => 507\n                \"nom\" => \"Cecilia\"\n                \"prenom\" => \"Alvarado\"\n              ]\n              #original: array:3 [\n                \"id\" => 507\n                \"nom\" => \"Cecilia\"\n                \"prenom\" => \"Alvarado\"\n              ]\n              #changes: []\n              #casts: array:2 [\n                \"email_verified_at\" => \"datetime\"\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: array:2 [\n                0 => \"password\"\n                1 => \"remember_token\"\n              ]\n              #visible: []\n              #fillable: []\n              #guarded: []\n              #rememberTokenName: \"remember_token\"\n              #cascadeDeletes: array:3 [\n                0 => \"notes\"\n                1 => \"info\"\n                2 => \"historique\"\n              ]\n              #accessToken: null\n              #forceDeleting: false\n            }\n            \"payment\" => App\\Models\\TypePayment {#2211\n              #connection: \"mysql\"\n              #table: \"type_payments\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Droit d'inscription\"\n              ]\n              #original: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Droit d'inscription\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"nom\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:11 [\n            0 => \"user_id\"\n            1 => \"type_payment_id\"\n            2 => \"moyen_payment_id\"\n            3 => \"type_encaissement_id\"\n            4 => \"annee_universitaire_id\"\n            5 => \"montant\"\n            6 => \"libelle\"\n            7 => \"is_valid\"\n            8 => \"is_valid_sec\"\n            9 => \"code\"\n            10 => \"created_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\HistoriquePayment {#2228\n          #connection: \"mysql\"\n          #table: \"historique_payments\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 3592\n            \"user_id\" => 507\n            \"type_payment_id\" => 2\n            \"moyen_payment_id\" => 2\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 11\n            \"code\" => \"Consectetur nesciunt\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:58:22\"\n            \"updated_at\" => \"2025-07-22 09:58:22\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #original: array:14 [\n            \"id\" => 3592\n            \"user_id\" => 507\n            \"type_payment_id\" => 2\n            \"moyen_payment_id\" => 2\n            \"type_encaissement_id\" => 1\n            \"annee_universitaire_id\" => 6\n            \"montant\" => 11\n            \"code\" => \"Consectetur nesciunt\"\n            \"is_valid\" => 0\n            \"is_valid_sec\" => 0\n            \"created_at\" => \"2025-07-22 09:58:22\"\n            \"updated_at\" => \"2025-07-22 09:58:22\"\n            \"deleted_at\" => null\n            \"libelle\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"user\" => App\\Models\\User {#2199}\n            \"payment\" => App\\Models\\TypePayment {#2266\n              #connection: \"mysql\"\n              #table: \"type_payments\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 2\n                \"nom\" => \"Fiche\"\n              ]\n              #original: array:2 [\n                \"id\" => 2\n                \"nom\" => \"Fiche\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: false\n              #hidden: []\n              #visible: []\n              #fillable: array:1 [\n                0 => \"nom\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:11 [\n            0 => \"user_id\"\n            1 => \"type_payment_id\"\n            2 => \"moyen_payment_id\"\n            3 => \"type_encaissement_id\"\n            4 => \"annee_universitaire_id\"\n            5 => \"montant\"\n            6 => \"libelle\"\n            7 => \"is_valid\"\n            8 => \"is_valid_sec\"\n            9 => \"code\"\n            10 => \"created_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => \"6\"\n    \"filtreRempli\" => \"\"\n    \"page\" => 10\n    \"paginators\" => array:1 [\n      \"page\" => 10\n    ]\n  ]\n  \"name\" => \"info-etu\"\n  \"view\" => \"livewire.secretaire.infoetu.index\"\n  \"component\" => \"App\\Http\\Livewire\\InfoEtu\"\n  \"id\" => \"NLhYCPItDtRIRyDL3Ka2\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestion/inscription\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753167149\n]"}, "request": {"path_info": "/livewire/message/info-etu", "status_code": "<pre class=sf-dump id=sf-dump-2121814583 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2121814583\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-985168945 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-985168945\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1949336520 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">NLhYCPItDtRIRyDL3Ka2</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">info-etu</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"19 characters\">gestion/inscription</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">6bd4e81f</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">edit</span>\"\n      \"<span class=sf-dump-key>editUser</span>\" => <span class=sf-dump-note>array:32</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>506</span>\n        \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Gillian</span>\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Robinson</span>\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>H</span>\"\n        \"<span class=sf-dump-key>date_naissance</span>\" => \"<span class=sf-dump-str title=\"8 characters\">07/08/81</span>\"\n        \"<span class=sf-dump-key>lieu_naissance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">QSFD</span>\"\n        \"<span class=sf-dump-key>nationalite</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>ville</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>pays</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>adresse</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>telephone1</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1234123</span>\"\n        \"<span class=sf-dump-key>telephone2</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_pere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_mere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cin</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>date_delivrance</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>lieu_delivrance</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duplicata</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>matricule</span>\" => \"<span class=sf-dump-str title=\"12 characters\">002/25/IMSAA</span>\"\n        \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>photo</span>\" => \"<span class=sf-dump-str title=\"25 characters\">media/avatars/avatar0.jpg</span>\"\n        \"<span class=sf-dump-key>inscription_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">22-07-2025</span>\"\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-num>5</span>\n        \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-22T06:57:48.000000Z</span>\"\n        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-22T06:59:42.000000Z</span>\"\n        \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_filled</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>tel_pere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_mere</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>nom_tuteur</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tel_tuteur</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payments</span>\" => []\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>filtreRempli</span>\" => \"\"\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>10</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>payments</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\HistoriquePayment</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>3589</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>3590</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">payment</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">a57a2aaaf828a17ea673434038083f4537fc15d12c3aa9ed717ed6965083df8c</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">fni6</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">goToEditUser</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>507</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949336520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-631015005 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1347</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/gestion/inscription?page=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkxBcUVSbzJ4czBrWSs3dXZGTXgrb0E9PSIsInZhbHVlIjoiOTVnTTAvZjE0TE12T0NzNDJqSllLUFFnMHhTVEpJUnhYT2dZVEFXL3VvOEt1UTdhMGVsY1JRUnJkaWpnL1hHSzNHL0pDeHhMaCtvcVEyRXE5RVVxNmVoWUZSRjc1M2JXeDEvdEsxaHFQWVYvOGpKWjFkZnNUQ0Vhc01PK0s5ejYiLCJtYWMiOiIyNDI2Y2MzZjRiYzFiOWQ1ODYwZmFlZWQ0YzA3MDc3MTZkMzk3MDc3MGIxNWQ1MzVmZDY2NWM0N2Y3MzQwZTJhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InRtNy9KVThaWjloWU0xWnZJdGZMTkE9PSIsInZhbHVlIjoiRUt4ZnhnSHRja2RySnNKMkQ2ZVNKOTlJRlZDTDdHSUNEOGZObmZzQ1FzeG9jYyttSEZRclVWMm9NcEF4WGQzZmhBbFdhbm42UVRiWVJETFgxMmVQY3B6K1RFa3doanRqbDhOeW1OclpBcUJicWd4WUdvb2NuMzdaUndaSXlvREoiLCJtYWMiOiIzMDkzZmUzZWIxOWM0NzU5MTdlZjI0ODczZWVhMTMzZTg2NTYxNDYzYWQxMDg0Y2E3ZmExZjU3NjljZWQ4ZjE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631015005\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-334995120 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56952</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/info-etu</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1347</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1347</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/gestion/inscription?page=10</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkxBcUVSbzJ4czBrWSs3dXZGTXgrb0E9PSIsInZhbHVlIjoiOTVnTTAvZjE0TE12T0NzNDJqSllLUFFnMHhTVEpJUnhYT2dZVEFXL3VvOEt1UTdhMGVsY1JRUnJkaWpnL1hHSzNHL0pDeHhMaCtvcVEyRXE5RVVxNmVoWUZSRjc1M2JXeDEvdEsxaHFQWVYvOGpKWjFkZnNUQ0Vhc01PK0s5ejYiLCJtYWMiOiIyNDI2Y2MzZjRiYzFiOWQ1ODYwZmFlZWQ0YzA3MDc3MTZkMzk3MDc3MGIxNWQ1MzVmZDY2NWM0N2Y3MzQwZTJhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InRtNy9KVThaWjloWU0xWnZJdGZMTkE9PSIsInZhbHVlIjoiRUt4ZnhnSHRja2RySnNKMkQ2ZVNKOTlJRlZDTDdHSUNEOGZObmZzQ1FzeG9jYyttSEZRclVWMm9NcEF4WGQzZmhBbFdhbm42UVRiWVJETFgxMmVQY3B6K1RFa3doanRqbDhOeW1OclpBcUJicWd4WUdvb2NuMzdaUndaSXlvREoiLCJtYWMiOiIzMDkzZmUzZWIxOWM0NzU5MTdlZjI0ODczZWVhMTMzZTg2NTYxNDYzYWQxMDg0Y2E3ZmExZjU3NjljZWQ4ZjE3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753167607.195</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753167607</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334995120\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-742234424 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wtgdgWdjyT0TX1yKLC0SW9faQdN6okxlgxE1VJm4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742234424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-562261977 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 07:00:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNET2kvWEdEcXQyWVBVc1d3dkdZc3c9PSIsInZhbHVlIjoiK3NiSGMzc1paSHRxL1VVUkY5YVdPTnlob3kwaE5GekFGdW1UaFE3V1NVMFhFeVZSbHJqR1lBMDYwVUFUVlJHOHdHVkU1NXd4a3lidWk5cUJrSFUzd0xCRDBzcW8xUFlCbTRuRitmVzdhbGhXYWhOemVhVWFKNVdRS2ZrOEFCNmIiLCJtYWMiOiIyMmE3ODE2ZWMyZTk3NjYxZGJlMzUwM2VjNWFhOWJjMjExZTg5YTBiMWFlYjkxNjU4MTI4OTU3NDNlMGE4ODNhIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 09:00:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImRqZmY0ZUQwMytRc1RBZFNBbjN2eVE9PSIsInZhbHVlIjoiVDJnSGRicURYbGNBQXorTXZUTEtXQWJUNDBVR0k1RkxkTXp5akxqMVVqWXVLNEpRR2UwdWh3UmtnQk1sMHpRbVM0VmZoL1lHYk41cnVYQ1VNV3lOWEM5RksvOUsySWJkcjRmeVd4MmIwRWt5Q09OYnhuYnRyQ2RaTlk3QzhMeloiLCJtYWMiOiI4OTBmMGFhYTcyZWIxNWJkYzY5YzBkNzc0OWI5OTU2MGM2ODBkMjk0NTcxNWExODVmNGQxZjBlN2JlMWU2YzI4IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 09:00:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNET2kvWEdEcXQyWVBVc1d3dkdZc3c9PSIsInZhbHVlIjoiK3NiSGMzc1paSHRxL1VVUkY5YVdPTnlob3kwaE5GekFGdW1UaFE3V1NVMFhFeVZSbHJqR1lBMDYwVUFUVlJHOHdHVkU1NXd4a3lidWk5cUJrSFUzd0xCRDBzcW8xUFlCbTRuRitmVzdhbGhXYWhOemVhVWFKNVdRS2ZrOEFCNmIiLCJtYWMiOiIyMmE3ODE2ZWMyZTk3NjYxZGJlMzUwM2VjNWFhOWJjMjExZTg5YTBiMWFlYjkxNjU4MTI4OTU3NDNlMGE4ODNhIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 09:00:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImRqZmY0ZUQwMytRc1RBZFNBbjN2eVE9PSIsInZhbHVlIjoiVDJnSGRicURYbGNBQXorTXZUTEtXQWJUNDBVR0k1RkxkTXp5akxqMVVqWXVLNEpRR2UwdWh3UmtnQk1sMHpRbVM0VmZoL1lHYk41cnVYQ1VNV3lOWEM5RksvOUsySWJkcjRmeVd4MmIwRWt5Q09OYnhuYnRyQ2RaTlk3QzhMeloiLCJtYWMiOiI4OTBmMGFhYTcyZWIxNWJkYzY5YzBkNzc0OWI5OTU2MGM2ODBkMjk0NTcxNWExODVmNGQxZjBlN2JlMWU2YzI4IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 09:00:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562261977\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1069617609 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ST29EDqqmrziZoBlBpIIz1yjI1cQGNKDk3qgbpIC</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/gestion/inscription</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753167149</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069617609\", {\"maxDepth\":0})</script>\n"}}